/**
 * 统一的图标组件包装器
 * 提供一致的图标使用接口和样式
 */

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { IconProps, getIconSize, getIconColor, ICON_SIZES } from '@/config/icons';

interface BaseIconProps extends IconProps {
  icon: LucideIcon;
  'aria-label'?: string;
  'aria-hidden'?: boolean;
}

/**
 * 基础图标组件
 */
export const Icon: React.FC<BaseIconProps> = ({
  icon: IconComponent,
  size = 'md',
  color = 'default',
  className,
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden = !ariaLabel,
  ...props
}) => {
  const iconSize = getIconSize(size);
  const iconColor = getIconColor(color);

  return (
    <IconComponent
      size={iconSize}
      className={cn(iconColor, className)}
      aria-label={ariaLabel}
      aria-hidden={ariaHidden}
      {...props}
    />
  );
};

/**
 * 可点击的图标按钮组件
 */
interface IconButtonProps extends BaseIconProps {
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'ghost' | 'outline' | 'solid';
  rounded?: boolean;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon: IconComponent,
  size = 'md',
  color = 'default',
  className,
  onClick,
  disabled = false,
  variant = 'ghost',
  rounded = true,
  'aria-label': ariaLabel,
  ...props
}) => {
  const iconSize = getIconSize(size);
  const iconColor = getIconColor(color);

  const baseClasses = cn(
    'inline-flex items-center justify-center transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-system-blue focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    {
      'rounded-full': rounded,
      'rounded-lg': !rounded,
    }
  );

  const variantClasses = {
    ghost: 'hover:bg-system-gray-6 active:bg-system-gray-5',
    outline: 'border border-system-gray-4 hover:border-system-gray-3 hover:bg-system-gray-6',
    solid: 'bg-system-blue text-white hover:bg-system-blue/90 active:bg-system-blue/80',
  };

  const sizeClasses = {
    xs: 'p-1',
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-2.5',
    xl: 'p-3',
    '2xl': 'p-4',
  };

  const sizeKey = typeof size === 'number' ? 'md' : size;

  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[sizeKey],
        className
      )}
      aria-label={ariaLabel}
      {...props}
    >
      <IconComponent
        size={iconSize}
        className={variant === 'solid' ? 'text-white' : iconColor}
      />
    </button>
  );
};

/**
 * 带标签的图标组件
 */
interface IconWithLabelProps extends BaseIconProps {
  label: string;
  labelPosition?: 'right' | 'left' | 'top' | 'bottom';
  gap?: 'sm' | 'md' | 'lg';
}

export const IconWithLabel: React.FC<IconWithLabelProps> = ({
  icon: IconComponent,
  label,
  labelPosition = 'right',
  gap = 'md',
  size = 'md',
  color = 'default',
  className,
  ...props
}) => {
  const iconSize = getIconSize(size);
  const iconColor = getIconColor(color);

  const gapClasses = {
    sm: 'gap-1',
    md: 'gap-2',
    lg: 'gap-3',
  };

  const containerClasses = cn(
    'inline-flex items-center',
    gapClasses[gap],
    {
      'flex-row': labelPosition === 'right',
      'flex-row-reverse': labelPosition === 'left',
      'flex-col': labelPosition === 'bottom',
      'flex-col-reverse': labelPosition === 'top',
    },
    className
  );

  return (
    <div className={containerClasses}>
      <IconComponent
        size={iconSize}
        className={iconColor}
        {...props}
      />
      <span className="text-sm font-sf-pro text-label">{label}</span>
    </div>
  );
};

/**
 * 状态图标组件（带颜色状态）
 */
interface StatusIconProps extends Omit<BaseIconProps, 'color'> {
  status: 'success' | 'warning' | 'error' | 'info' | 'pending';
  showBackground?: boolean;
}

export const StatusIcon: React.FC<StatusIconProps> = ({
  icon: IconComponent,
  status,
  showBackground = false,
  size = 'md',
  className,
  ...props
}) => {
  const iconSize = getIconSize(size);

  const statusColors = {
    success: 'text-system-green',
    warning: 'text-system-orange',
    error: 'text-system-red',
    info: 'text-system-blue',
    pending: 'text-system-gray-2',
  };

  const backgroundColors = {
    success: 'bg-system-green/10',
    warning: 'bg-system-orange/10',
    error: 'bg-system-red/10',
    info: 'bg-system-blue/10',
    pending: 'bg-system-gray-6',
  };

  const iconClasses = cn(
    statusColors[status],
    {
      'p-2 rounded-full': showBackground,
    },
    showBackground && backgroundColors[status],
    className
  );

  return (
    <div className={showBackground ? 'inline-flex' : 'inline'}>
      <IconComponent
        size={iconSize}
        className={iconClasses}
        {...props}
      />
    </div>
  );
};

/**
 * 加载状态图标组件
 */
interface LoadingIconProps extends Omit<BaseIconProps, 'icon'> {
  spinning?: boolean;
}

export const LoadingIcon: React.FC<LoadingIconProps> = ({
  spinning = true,
  size = 'md',
  color = 'default',
  className,
  ...props
}) => {
  const iconSize = getIconSize(size);
  const iconColor = getIconColor(color);

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        {
          'animate-spin': spinning,
        },
        className
      )}
      {...props}
    >
      <svg
        width={iconSize}
        height={iconSize}
        viewBox="0 0 24 24"
        fill="none"
        className={iconColor}
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="32"
          strokeDashoffset="32"
        >
          <animate
            attributeName="stroke-dasharray"
            dur="2s"
            values="0 32;16 16;0 32;0 32"
            repeatCount="indefinite"
          />
          <animate
            attributeName="stroke-dashoffset"
            dur="2s"
            values="0;-16;-32;-32"
            repeatCount="indefinite"
          />
        </circle>
      </svg>
    </div>
  );
};


