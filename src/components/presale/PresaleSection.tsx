'use client';

import React, { useState, useEffect, memo, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Card, Icon } from '@/components/ui';
import {
  FeatureIcons,
  UserIcons,
  FinanceIcons,
  RewardIcons,
  ActionIcons
} from '@/config/icons';
import { useToast } from '@/components/ui';
import { usePresale } from '@/hooks/usePresale';
import { useOptimizedQuery } from '@/hooks/useOptimizedQuery';
import SocialBinding from '@/components/social/SocialBinding';
import TelegramChannelJoin from '@/components/telegram/TelegramChannelJoin';
import WhitepaperModal from '@/components/whitepaper/WhitepaperModal';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { checkTelegramBinding } from '@/lib/telegramVerification';

// 优化的倒计时组件
const CountdownTimer = memo<{ timeLeft: { days: number; hours: number; minutes: number; seconds: number } }>(
  ({ timeLeft }) => {
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
      setIsClient(true);
    }, []);

    const timeItems = useMemo(() => [
      { label: '天', value: timeLeft.days },
      { label: '时', value: timeLeft.hours },
      { label: '分', value: timeLeft.minutes },
      { label: '秒', value: timeLeft.seconds }
    ], [timeLeft]);

    // 服务器端渲染时显示占位符
    if (!isClient) {
      return (
        <div className="grid grid-cols-4 gap-4">
          {['天', '时', '分', '秒'].map((label, index) => (
            <div key={label} className="text-center">
              <div className="bg-gradient-to-br from-system-red to-system-orange text-white rounded-2xl p-4 mb-2">
                <span className="text-title-2 font-sf-pro font-bold">
                  --
                </span>
              </div>
              <span className="text-caption-1 text-secondary-label">
                {label}
              </span>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-4 gap-4">
        {timeItems.map((item, index) => (
          <motion.div
            key={item.label}
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="text-center"
          >
            <div className="bg-gradient-to-br from-system-red to-system-orange text-white rounded-2xl p-4 mb-2">
              <span className="text-title-2 font-sf-pro font-bold">
                {item.value.toString().padStart(2, '0')}
              </span>
            </div>
            <span className="text-caption-1 text-secondary-label">
              {item.label}
            </span>
          </motion.div>
        ))}
      </div>
    );
  }
);

CountdownTimer.displayName = 'CountdownTimer';

// 优化的价格信息组件（支持新的动态定价）
const PriceInfo = memo<{
  currentPrice: number;
  nextPrice: number;
  currentRate: number;
  nextRate: number;
  currentStage: number;
  totalStages: number;
  bnbUsdPrice?: number;
}>(({ currentPrice, nextPrice, currentRate, nextRate, currentStage, totalStages, bnbUsdPrice = 850 }) => {
  const priceIncreasePercentage = ((nextPrice - currentPrice) / currentPrice) * 100;

  return (
    <div className="space-y-6 mb-8">
      {/* 阶段信息 */}
      <div className="text-center p-4 bg-blue-50 rounded-xl border border-blue-200">
        <div className="text-sm text-secondary-label mb-1">当前阶段</div>
        <div className="text-xl font-bold text-blue-600">
          第 {currentStage} / {totalStages} 阶段
        </div>
        <div className="text-xs text-blue-500 mt-1">
          每完成一个阶段，价格上涨2%
        </div>
      </div>

      {/* 价格信息 */}
      <div className="grid md:grid-cols-2 gap-6">
        <div className="text-center p-6 bg-system-green/10 rounded-2xl border border-system-green/20">
          <div className="text-caption-1 text-secondary-label mb-2">当前价格</div>
          <div className="text-title-2 font-sf-pro font-bold text-system-green">
            ${currentPrice.toFixed(6)}
          </div>
          <div className="text-caption-1 text-system-green mt-1">
            每个 HAOX
          </div>
          <div className="text-xs text-secondary-label mt-2">
            {currentRate.toLocaleString()} HAOX/BNB
          </div>
        </div>

        <div className="text-center p-6 bg-system-red/10 rounded-2xl border border-system-red/20">
          <div className="text-caption-1 text-secondary-label mb-2">下阶段价格</div>
          <div className="text-title-2 font-sf-pro font-bold text-system-red">
            ${nextPrice.toFixed(6)}
          </div>
          <div className="text-caption-1 text-system-red mt-1">
            +{priceIncreasePercentage.toFixed(1)}% 涨幅
          </div>
          <div className="text-xs text-secondary-label mt-2">
            {nextRate.toLocaleString()} HAOX/BNB
          </div>
        </div>
      </div>

      {/* BNB参考价格 */}
      <div className="text-center p-3 bg-gray-50 rounded-lg">
        <div className="text-xs text-secondary-label">
          参考价格基于 BNB = ${bnbUsdPrice} USD
        </div>
      </div>
    </div>
  );
});

PriceInfo.displayName = 'PriceInfo';

const PresaleSection: React.FC = () => {
  const { addToast } = useToast();
  const { presaleData, stats, copyWalletAddress, getPresaleStatus } = usePresale();
  const { user, isAuthenticated } = useTelegramAuth();

  const [copied, setCopied] = useState(false);
  const [currentPurchaseIndex, setCurrentPurchaseIndex] = useState(0);
  const [showTelegramJoin, setShowTelegramJoin] = useState(false);
  const [telegramJoinComplete, setTelegramJoinComplete] = useState(false);
  const [presaleEligible, setPresaleEligible] = useState(false);
  const [showWhitepaper, setShowWhitepaper] = useState(false);



  // 优化的动画效果
  const animatedPurchaseIndex = useMemo(() => currentPurchaseIndex, [currentPurchaseIndex]);

  // Animate recent purchases
  useEffect(() => {
    if (presaleData.recentPurchases.length === 0) return;

    const purchaseTimer = setInterval(() => {
      setCurrentPurchaseIndex(prev => (prev + 1) % presaleData.recentPurchases.length);
    }, 3000);

    return () => clearInterval(purchaseTimer);
  }, [presaleData.recentPurchases.length]);

  const handleCopyAddress = useCallback(async () => {
    const success = await copyWalletAddress();
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  }, [copyWalletAddress]);

  // 检查用户预售资格
  const checkPresaleEligibility = useCallback(async () => {
    if (!user?.id) return false;

    try {
      const binding = await checkTelegramBinding(user.id.toString());
      setPresaleEligible(binding.presaleEligible);
      setTelegramJoinComplete(binding.isBound);
      return binding.presaleEligible;
    } catch (error) {
      console.error('Failed to check eligibility:', error);
      return false;
    }
  }, [user?.id]);

  // 处理预售参与
  const handlePresaleParticipation = useCallback(async () => {
    if (!isAuthenticated) {
      addToast({
        title: '请先登录',
        message: '参与预售需要先通过Telegram登录',
        type: 'error',
      });
      return;
    }

    const isEligible = await checkPresaleEligibility();

    if (!isEligible) {
      setShowTelegramJoin(true);
      return;
    }

    // 如果已经有资格，直接进行预售
    addToast({
      title: '预售功能开发中',
      message: '预售功能正在开发中，敬请期待',
      type: 'info',
    });
  }, [isAuthenticated, checkPresaleEligibility, addToast]);

  // Telegram频道加入完成回调
  const handleTelegramJoinComplete = useCallback((success: boolean) => {
    if (success) {
      setTelegramJoinComplete(true);
      addToast({
        title: '加入成功！',
        message: '您已成功加入 SocioMint 官方频道',
        type: 'success',
      });
    }
  }, [addToast]);

  // 获得预售资格回调
  const handleEligibilityGranted = useCallback(() => {
    setPresaleEligible(true);
    setShowTelegramJoin(false);
    addToast({
      title: '恭喜！',
      message: '您已获得预售认购资格',
      type: 'success',
    });
  }, [addToast]);

  // 初始化时检查预售资格
  useEffect(() => {
    if (user?.id && isAuthenticated) {
      checkPresaleEligibility();
    }
  }, [user?.id, isAuthenticated, checkPresaleEligibility]);

  return (
    <section className="relative py-20 bg-gradient-to-br from-system-red/5 via-system-orange/5 to-system-yellow/5 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-10 w-32 h-32 bg-system-orange/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-system-red/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-system-yellow/5 rounded-full blur-3xl animate-pulse delay-500" />
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Icon icon={RewardIcons.zap} size="lg" className="text-system-red animate-pulse" />
            <span className="text-system-red font-sf-pro font-bold text-body uppercase tracking-wider">
              限时预售
            </span>
            <Icon icon={RewardIcons.zap} size="lg" className="text-system-red animate-pulse" />
          </div>
          
          <h2 className="text-title-1 font-sf-pro font-bold text-label mb-4">
            HAOX 代币预售
            <span className="text-system-red"> 火热进行中</span>
          </h2>
          
          <p className="text-title-3 text-secondary-label max-w-3xl mx-auto">
            抢先获得 HAOX 代币，享受早期投资者专属价格优势
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Presale Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <Card className="relative overflow-hidden border-2 border-system-red/20 bg-gradient-to-br from-white to-system-red/5">
              {/* Urgency Banner */}
              <div className="absolute top-0 right-0 bg-gradient-to-r from-system-red to-system-orange text-white px-6 py-2 transform rotate-12 translate-x-4 -translate-y-2">
                <span className="text-caption-1 font-bold">限时优惠</span>
              </div>

              <div className="p-8">
                {/* Countdown Timer */}
                <div className="mb-8">
                  <div className="flex items-center space-x-2 mb-4">
                    <Icon icon={FeatureIcons.clock} size="md" color="error" />
                    <span className="text-headline font-sf-pro font-semibold text-label">
                      预售倒计时
                    </span>
                  </div>
                  
                  <CountdownTimer timeLeft={stats.timeLeft} />
                </div>

                {/* Price Information */}
                <PriceInfo
                  currentPrice={presaleData.currentPrice}
                  nextPrice={presaleData.nextPrice}
                  currentRate={presaleData.currentRate || 1_912_125}
                  nextRate={presaleData.nextRate || 1_873_683}
                  currentStage={presaleData.currentStage || 1}
                  totalStages={presaleData.totalStages || 100}
                  bnbUsdPrice={presaleData.bnbUsdPrice || 850}
                />

                {/* Progress Bar */}
                <div className="mb-8">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-body font-sf-pro font-medium text-label">
                      筹资进度
                    </span>
                    <span className="text-body font-sf-pro font-bold text-system-blue">
                      {stats.progressPercentage.toFixed(1)}%
                    </span>
                  </div>
                  
                  <div className="w-full bg-system-gray-5 rounded-full h-4 overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${stats.progressPercentage}%` }}
                      transition={{ duration: 1.5, ease: "easeOut" }}
                      className="h-full bg-gradient-to-r from-system-blue to-system-purple rounded-full relative"
                    >
                      <div className="absolute inset-0 bg-white/20 animate-pulse" />
                    </motion.div>
                  </div>
                  
                  <div className="flex justify-between text-caption-1 text-secondary-label mt-2">
                    <span>${presaleData.totalRaised.toLocaleString()}</span>
                    <span>${presaleData.targetAmount.toLocaleString()}</span>
                  </div>
                </div>

                {/* Wallet Address */}
                <div className="mb-8">
                  <div className="text-body font-sf-pro font-medium text-label mb-3">
                    预售合约地址
                  </div>
                  <div className="flex items-center space-x-3 p-4 bg-system-gray-6 rounded-xl">
                    <code className="flex-1 text-body font-mono text-label break-all">
                      {presaleData.walletAddress}
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyAddress}
                      className="flex items-center space-x-2"
                    >
                      {copied ? (
                        <Icon icon={ActionIcons.checkCircle} size="sm" color="success" />
                      ) : (
                        <Icon icon={ActionIcons.copy} size="sm" />
                      )}
                      <span>{copied ? '已复制' : '复制'}</span>
                    </Button>
                  </div>
                </div>

                {/* CTA Buttons */}
                <div className="grid md:grid-cols-2 gap-4">
                  <Button
                    size="lg"
                    onClick={handlePresaleParticipation}
                    className="bg-gradient-to-r from-system-red to-system-orange hover:from-system-red/90 hover:to-system-orange/90 text-white font-bold"
                  >
                    <Icon icon={RewardIcons.zap} size="md" className="mr-2" />
                    {presaleEligible ? '立即参与预售' : '获取预售资格'}
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => setShowWhitepaper(true)}
                  >
                    <Icon icon={UserIcons.shield} size="md" className="mr-2" />
                    查看白皮书
                  </Button>
                </div>
              </div>
            </Card>
          </motion.div>

          {/* Side Stats */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {/* Participant Count */}
            <Card className="p-6 text-center bg-gradient-to-br from-system-blue/5 to-system-purple/5">
              <div className="flex items-center justify-center space-x-2 mb-3">
                <Icon icon={UserIcons.users} size="lg" color="primary" />
                <span className="text-headline font-sf-pro font-semibold text-label">
                  参与人数
                </span>
              </div>
              <motion.div
                key={presaleData.participantCount}
                initial={{ scale: 1.2, color: '#007AFF' }}
                animate={{ scale: 1, color: '#1D1D1F' }}
                transition={{ duration: 0.3 }}
                className="text-title-1 font-sf-pro font-bold text-label"
              >
                {presaleData.participantCount.toLocaleString()}
              </motion.div>
              <div className="text-caption-1 text-system-blue mt-1">
                +8 人在线
              </div>
            </Card>

            {/* Recent Activity */}
            <Card className="p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Icon icon={FinanceIcons.trendUp} size="md" color="success" />
                <span className="text-headline font-sf-pro font-semibold text-label">
                  最新购买
                </span>
              </div>
              
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPurchaseIndex}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-3"
                >
                  {presaleData.recentPurchases.map((purchase, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-xl ${
                        index === currentPurchaseIndex 
                          ? 'bg-system-green/10 border border-system-green/20' 
                          : 'bg-system-gray-6'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span className="text-caption-1 text-secondary-label">
                          {purchase.address}
                        </span>
                        <span className="text-caption-1 font-sf-pro font-bold text-system-green">
                          {purchase.amount.toLocaleString()} HAOX
                        </span>
                      </div>
                      <div className="text-caption-2 text-tertiary-label">
                        {Math.floor((Date.now() - purchase.timestamp.getTime()) / 60000)} 分钟前
                      </div>
                    </div>
                  ))}
                </motion.div>
              </AnimatePresence>
            </Card>

            {/* Benefits */}
            <Card className="p-6">
              <div className="flex items-center space-x-2 mb-4">
                <Icon icon={RewardIcons.star} size="md" color="warning" />
                <span className="text-headline font-sf-pro font-semibold text-label">
                  预售优势
                </span>
              </div>
              
              <div className="space-y-3">
                {[
                  { icon: '🎯', text: '最低价格保证' },
                  { icon: '🚀', text: '优先交易权限' },
                  { icon: '💎', text: '额外奖励代币' },
                  { icon: '🔒', text: '智能合约保护' }
                ].map((benefit, index) => (
                  <motion.div
                    key={benefit.text}
                    initial={{ opacity: 0, x: -10 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-3"
                  >
                    <span className="text-title-3">{benefit.icon}</span>
                    <span className="text-body text-label">{benefit.text}</span>
                  </motion.div>
                ))}
              </div>
            </Card>

            {/* Warning */}
            <Card className="p-4 bg-system-yellow/10 border border-system-yellow/20">
              <div className="flex items-start space-x-3">
                <Icon icon={ActionIcons.alert} size="md" color="warning" className="mt-0.5" />
                <div>
                  <div className="text-caption-1 font-sf-pro font-semibold text-system-yellow mb-1">
                    投资提醒
                  </div>
                  <div className="text-caption-2 text-secondary-label">
                    数字资产投资存在风险，请理性投资，仅投入您能承受损失的资金。
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Telegram频道加入模态框 */}
        <AnimatePresence>
          {showTelegramJoin && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              onClick={() => setShowTelegramJoin(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold">获取预售资格</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowTelegramJoin(false)}
                    >
                      ✕
                    </Button>
                  </div>

                  <div className="mb-6">
                    <p className="text-gray-600">
                      加入我们的官方 Telegram 频道即可获得预售资格。
                      频道将为您提供最新的项目资讯和独家预售信息。
                    </p>
                  </div>

                  <TelegramChannelJoin
                    onJoinComplete={handleTelegramJoinComplete}
                    onEligibilityGranted={handleEligibilityGranted}
                  />
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 白皮书模态框 */}
      <WhitepaperModal
        isOpen={showWhitepaper}
        onClose={() => setShowWhitepaper(false)}
      />
    </section>
  );
};

export default PresaleSection;
