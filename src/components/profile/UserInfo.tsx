'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, IconButton, Modal, Input } from '@/components/ui';
import {
  UserIcons,
  ActionIcons,
  FinanceIcons,
  SocialIcons,
  FeatureIcons,
  NotificationIcons
} from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';

import type { UserProfile } from '@/types/user';
import { cn } from '@/lib/utils';

interface UserInfoProps {
  user: UserProfile | null;
}

const UserInfo: React.FC<UserInfoProps> = ({ user }) => {
  const { user: telegramUser } = useTelegramAuth();

  // 简单的地址截断函数
  const truncateAddress = (address: string) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editForm, setEditForm] = useState(() => ({
    nickname: user?.nickname || '',
    email: user?.email || '',
    bio: user?.bio || ''
  }));
  const [isLoading, setIsLoading] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  // 当用户数据变化时更新表单
  useEffect(() => {
    if (user) {
      setEditForm({
        nickname: user.nickname || '',
        email: user.email || '',
        bio: user.bio || ''
      });
    }
  }, [user]);

  const copyAddress = async () => {
    if (user?.address) {
      try {
        await navigator.clipboard.writeText(user.address);
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000);
      } catch (error) {
        console.error('Failed to copy address:', error);
      }
    }
  };

  const handleEditSubmit = async () => {
    setIsLoading(true);
    try {
      // TODO: 实现用户资料更新功能
      console.log('更新用户资料:', editForm);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <Card title="用户信息">
        <div className="p-6 text-center">
          <p className="text-body text-secondary-label">
            正在加载用户信息...
          </p>
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className="bg-gradient-to-r from-system-blue/5 to-system-purple/5 border-system-blue/20">
        <div className="p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            {/* 用户基本信息 */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-20 h-20 rounded-full overflow-hidden border-4 border-white shadow-apple">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.nickname || 'User'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-system-gray-5 flex items-center justify-center text-system-gray text-2xl font-semibold">
                      {(user.nickname || 'U').charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-system-green rounded-full border-2 border-white flex items-center justify-center">
                  <Icon icon={UserIcons.verified} size="xs" className="text-white" />
                </div>
              </div>

              <div>
                <div className="flex items-center space-x-2 mb-1">
                  <h2 className="text-title-2 font-sf-pro font-bold text-label">
                    {user.nickname || '未设置昵称'}
                  </h2>
                  <IconButton
                    icon={ActionIcons.edit}
                    size="sm"
                    variant="ghost"
                    onClick={() => setIsEditModalOpen(true)}
                    aria-label="编辑资料"
                  />
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <p className="text-body font-mono text-secondary-label">
                    {truncateAddress(user.address)}
                  </p>
                  <IconButton
                    icon={isCopied ? ActionIcons.check : ActionIcons.copy}
                    size="xs"
                    variant="ghost"
                    onClick={copyAddress}
                    color={isCopied ? 'success' : 'muted'}
                    aria-label={isCopied ? '已复制' : '复制地址'}
                  />
                </div>

                {user.email && (
                  <p className="text-caption-1 text-secondary-label">
                    {user.email}
                  </p>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="md"
                onClick={() => setIsEditModalOpen(true)}
                className="flex items-center space-x-2"
              >
                <Icon icon={ActionIcons.edit} size="sm" />
                <span>编辑资料</span>
              </Button>

              <Button
                variant="primary"
                size="md"
                className="flex items-center space-x-2"
              >
                <Icon icon={SocialIcons.share} size="sm" />
                <span>分享</span>
              </Button>
            </div>
          </div>

          {/* 用户统计 */}
          <div className="mt-6 pt-6 border-t border-system-gray-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-title-3 font-sf-pro font-bold text-system-blue">
                  {Math.floor(Math.random() * 50) + 10}
                </p>
                <p className="text-caption-1 text-secondary-label">邀请人数</p>
              </div>
              
              <div className="text-center">
                <p className="text-title-3 font-sf-pro font-bold text-system-green">
                  {Math.floor(Math.random() * 20) + 5}
                </p>
                <p className="text-caption-1 text-secondary-label">完成任务</p>
              </div>
              
              <div className="text-center">
                <p className="text-title-3 font-sf-pro font-bold text-system-orange">
                  {Math.floor(Math.random() * 100) + 50}
                </p>
                <p className="text-caption-1 text-secondary-label">交易次数</p>
              </div>
              
              <div className="text-center">
                <p className="text-title-3 font-sf-pro font-bold text-system-purple">
                  {(() => {
                    try {
                      if (!user?.createdAt) return 0;
                      const createdDate = user.createdAt instanceof Date
                        ? user.createdAt
                        : new Date(user.createdAt);
                      return Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
                    } catch (error) {
                      console.warn('计算加入天数时出错:', error);
                      return 0;
                    }
                  })()}
                </p>
                <p className="text-caption-1 text-secondary-label">加入天数</p>
              </div>
            </div>
          </div>

          {/* 社交账户绑定状态 */}
          <div className="mt-6 pt-6 border-t border-system-gray-4">
            <h3 className="text-headline font-sf-pro font-semibold text-label mb-4">
              社交账户
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              <div className="flex items-center justify-between p-3 bg-system-gray-6 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Icon icon={SocialIcons.twitter} size="sm" color="primary" />
                  <span className="text-sm text-label">Twitter</span>
                </div>
                <Button variant="ghost" size="sm" className="text-xs">
                  绑定
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-system-gray-6 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Icon icon={SocialIcons.telegram} size="sm" color="primary" />
                  <span className="text-sm text-label">Telegram</span>
                </div>
                <Button variant="ghost" size="sm" className="text-xs">
                  绑定
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 bg-system-gray-6 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Icon icon={NotificationIcons.message} size="sm" color="primary" />
                  <span className="text-sm text-label">Discord</span>
                </div>
                <Button variant="ghost" size="sm" className="text-xs">
                  绑定
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* 编辑资料模态框 */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="编辑个人资料"
        size="md"
      >
        <div className="p-6 space-y-6">
          <div className="text-center">
            <div className="w-20 h-20 rounded-full overflow-hidden mx-auto mb-4">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.nickname || 'User'}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-system-gray-5 flex items-center justify-center text-system-gray text-2xl font-semibold">
                  {(user.nickname || 'U').charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <Button variant="outline" size="sm">
              更换头像
            </Button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-sf-pro font-medium text-label mb-2">
                昵称
              </label>
              <Input
                placeholder="输入您的昵称"
                value={editForm.nickname}
                onChange={(value) => setEditForm(prev => ({ ...prev, nickname: value }))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-sf-pro font-medium text-label mb-2">
                邮箱
              </label>
              <Input
                type="email"
                placeholder="输入您的邮箱"
                value={editForm.email}
                onChange={(value) => setEditForm(prev => ({ ...prev, email: value }))}
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-sf-pro font-medium text-label mb-2">
                个人简介
              </label>
              <textarea
                placeholder="介绍一下自己..."
                value={editForm.bio}
                onChange={(e) => setEditForm(prev => ({ ...prev, bio: e.target.value }))}
                className="w-full p-3 border border-system-gray-4 rounded-lg resize-none h-20 text-sm"
                maxLength={200}
              />
              <p className="text-xs text-secondary-label mt-1">
                {editForm.bio.length}/200
              </p>
            </div>
          </div>

          <div className="flex space-x-3">
            <Button
              variant="outline"
              size="lg"
              onClick={() => setIsEditModalOpen(false)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              variant="primary"
              size="lg"
              onClick={handleEditSubmit}
              loading={isLoading}
              className="flex-1"
            >
              保存
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default UserInfo;
