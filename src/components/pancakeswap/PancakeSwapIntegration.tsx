'use client';

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FinanceIcons, FeatureIcons } from '@/config/icons';
import { useToast } from '@/components/ui';
import { cn } from '@/lib/utils';

interface PancakeSwapIntegrationProps {
  tokenAddress?: string;
  className?: string;
}

const PancakeSwapIntegration: React.FC<PancakeSwapIntegrationProps> = ({ 
  tokenAddress = '0x...', // HAOX代币地址，需要替换为实际地址
  className 
}) => {
  const { addToast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // PancakeSwap相关链接
  const pancakeSwapUrls = {
    swap: `https://pancakeswap.finance/swap?outputCurrency=${tokenAddress}`,
    addLiquidity: `https://pancakeswap.finance/add/${tokenAddress}`,
    pool: `https://pancakeswap.finance/pools`,
    info: `https://pancakeswap.finance/info/tokens/${tokenAddress}`,
  };

  /**
   * 打开PancakeSwap交易页面
   */
  const handleSwap = useCallback(() => {
    try {
      setIsLoading(true);
      window.open(pancakeSwapUrls.swap, '_blank', 'noopener,noreferrer');
      
      addToast({
        type: 'info',
        title: '跳转到PancakeSwap',
        message: '正在打开PancakeSwap交易页面...',
      });
    } catch (error) {
      console.error('Failed to open PancakeSwap:', error);
      addToast({
        type: 'error',
        title: '跳转失败',
        message: '无法打开PancakeSwap页面，请稍后重试',
      });
    } finally {
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [pancakeSwapUrls.swap, addToast]);

  /**
   * 打开添加流动性页面
   */
  const handleAddLiquidity = useCallback(() => {
    try {
      window.open(pancakeSwapUrls.addLiquidity, '_blank', 'noopener,noreferrer');
      
      addToast({
        type: 'info',
        title: '跳转到PancakeSwap',
        message: '正在打开添加流动性页面...',
      });
    } catch (error) {
      console.error('Failed to open PancakeSwap liquidity:', error);
      addToast({
        type: 'error',
        title: '跳转失败',
        message: '无法打开流动性页面，请稍后重试',
      });
    }
  }, [pancakeSwapUrls.addLiquidity, addToast]);

  /**
   * 打开代币信息页面
   */
  const handleViewInfo = useCallback(() => {
    try {
      window.open(pancakeSwapUrls.info, '_blank', 'noopener,noreferrer');
      
      addToast({
        type: 'info',
        title: '跳转到PancakeSwap',
        message: '正在打开代币信息页面...',
      });
    } catch (error) {
      console.error('Failed to open PancakeSwap info:', error);
      addToast({
        type: 'error',
        title: '跳转失败',
        message: '无法打开代币信息页面，请稍后重试',
      });
    }
  }, [pancakeSwapUrls.info, addToast]);

  /**
   * 复制代币地址
   */
  const handleCopyAddress = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(tokenAddress);
      addToast({
        type: 'success',
        title: '复制成功',
        message: '代币地址已复制到剪贴板',
      });
    } catch (error) {
      console.error('Failed to copy address:', error);
      addToast({
        type: 'error',
        title: '复制失败',
        message: '无法复制代币地址，请手动复制',
      });
    }
  }, [tokenAddress, addToast]);

  return (
    <Card className={cn('p-6', className)}>
      <div className="space-y-6">
        {/* 标题 */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-3">
            <div className="w-12 h-12 bg-gradient-to-r from-system-orange to-system-yellow rounded-full flex items-center justify-center">
              <Icon icon={FinanceIcons.exchange} size="lg" color="white" />
            </div>
          </div>
          <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
            PancakeSwap 交易
          </h3>
          <p className="text-body text-secondary-label">
            在去中心化交易所交易 HAOX 代币
          </p>
        </div>

        {/* 代币信息 */}
        <div className="bg-system-gray-6 rounded-xl p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-caption-1 text-secondary-label">代币地址</span>
            <Button
              variant="ghost"
              size="xs"
              onClick={handleCopyAddress}
              className="p-1"
            >
              <Icon icon={ActionIcons.copy} size="sm" />
            </Button>
          </div>
          <p className="text-body font-mono text-label break-all">
            {tokenAddress}
          </p>
        </div>

        {/* 交易按钮 */}
        <div className="space-y-3">
          <Button
            variant="primary"
            size="lg"
            onClick={handleSwap}
            disabled={isLoading}
            className="w-full"
          >
            <Icon icon={FinanceIcons.exchange} size="sm" className="mr-2" />
            {isLoading ? '跳转中...' : '立即交易 HAOX'}
          </Button>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="md"
              onClick={handleAddLiquidity}
              className="flex-1"
            >
              <Icon icon={FinanceIcons.plus} size="sm" className="mr-1" />
              添加流动性
            </Button>
            
            <Button
              variant="outline"
              size="md"
              onClick={handleViewInfo}
              className="flex-1"
            >
              <Icon icon={FeatureIcons.info} size="sm" className="mr-1" />
              代币信息
            </Button>
          </div>
        </div>

        {/* 提示信息 */}
        <div className="bg-system-blue/10 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <Icon icon={FeatureIcons.info} size="sm" color="primary" className="mt-0.5" />
            <div>
              <p className="text-caption-1 text-system-blue font-medium mb-1">
                交易提示
              </p>
              <ul className="text-caption-1 text-system-blue space-y-1">
                <li>• 确保钱包已连接到BSC网络</li>
                <li>• 交易前请检查滑点设置</li>
                <li>• 建议设置2-5%的滑点容忍度</li>
                <li>• 注意Gas费用和网络拥堵情况</li>
              </ul>
            </div>
          </div>
        </div>

        {/* PancakeSwap品牌信息 */}
        <div className="text-center pt-2 border-t border-system-gray-4">
          <p className="text-caption-1 text-secondary-label">
            由 
            <span className="text-system-orange font-medium mx-1">PancakeSwap</span>
            提供技术支持
          </p>
        </div>
      </div>
    </Card>
  );
};

export default PancakeSwapIntegration;
