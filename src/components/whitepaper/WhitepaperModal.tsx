'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FeatureIcons } from '@/config/icons';
import { useToast } from '@/components/ui';
import { cn } from '@/lib/utils';

interface WhitepaperModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const WhitepaperModal: React.FC<WhitepaperModalProps> = ({ isOpen, onClose }) => {
  const [selectedLanguage, setSelectedLanguage] = useState<'zh' | 'en'>('zh');
  const [isClient, setIsClient] = useState(false);
  const { addToast } = useToast();

  // 确保只在客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDownload = async (language: 'zh' | 'en') => {
    if (!isClient) return;

    try {
      const filename = language === 'zh'
        ? 'HAOX_WHITEPAPER_V2.md'
        : 'HAOX_WHITEPAPER_V2_EN.md';

      // 创建下载链接
      const link = document.createElement('a');
      link.href = `/api/whitepaper?lang=${language}`;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      addToast({
        type: 'success',
        title: '下载开始',
        message: '白皮书下载已开始',
      });
    } catch (error) {
      console.error('Download failed:', error);
      addToast({
        type: 'error',
        title: '下载失败',
        message: '白皮书下载失败，请稍后重试',
      });
    }
  };

  const handleView = async (language: 'zh' | 'en') => {
    if (!isClient) return;

    try {
      // 在新窗口中打开白皮书
      const newWindow = window.open(`/api/whitepaper?lang=${language}`, '_blank', 'noopener,noreferrer');

      if (!newWindow) {
        addToast({
          type: 'error',
          title: '打开失败',
          message: '无法打开新窗口，请检查浏览器弹窗设置',
        });
      }
    } catch (error) {
      console.error('View failed:', error);
      addToast({
        type: 'error',
        title: '打开失败',
        message: '无法打开白皮书，请稍后重试',
      });
    }
  };

  // 只在客户端且模态框打开时渲染
  if (!isClient || !isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* 背景遮罩 */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* 模态框内容 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-md mx-4"
        >
          <Card className="p-6">
            {/* 标题栏 */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-title-3 font-sf-pro font-semibold text-label">
                HAOX 白皮书
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="p-2"
              >
                <Icon icon={ActionIcons.close} size="sm" />
              </Button>
            </div>

            {/* 语言选择 */}
            <div className="mb-6">
              <p className="text-body text-secondary-label mb-3">选择语言版本：</p>
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant={selectedLanguage === 'zh' ? 'primary' : 'outline'}
                  size="md"
                  onClick={() => setSelectedLanguage('zh')}
                  className="flex items-center justify-center"
                >
                  <span className="mr-2">🇨🇳</span>
                  中文版
                </Button>
                <Button
                  variant={selectedLanguage === 'en' ? 'primary' : 'outline'}
                  size="md"
                  onClick={() => setSelectedLanguage('en')}
                  className="flex items-center justify-center"
                >
                  <span className="mr-2">🇺🇸</span>
                  English
                </Button>
              </div>
            </div>

            {/* 白皮书信息 */}
            <div className="bg-system-gray-6 rounded-xl p-4 mb-6">
              <div className="flex items-start space-x-3">
                <Icon icon={FeatureIcons.document} size="md" color="primary" />
                <div>
                  <h4 className="text-body font-sf-pro font-medium text-label mb-1">
                    {selectedLanguage === 'zh' ? 'HAOX 白皮书 V2.0' : 'HAOX Whitepaper V2.0'}
                  </h4>
                  <p className="text-caption-1 text-secondary-label mb-2">
                    {selectedLanguage === 'zh' 
                      ? '连接下一个十亿用户，让Web3像呼吸一样自然' 
                      : 'Connecting the Next Billion Users, Making Web3 as Natural as Breathing'
                    }
                  </p>
                  <div className="flex items-center space-x-4 text-caption-1 text-secondary-label">
                    <span>📄 {selectedLanguage === 'zh' ? '完整版' : 'Full Version'}</span>
                    <span>📅 2025.02.02</span>
                    <span>🔄 V2.0</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <Button
                variant="primary"
                size="lg"
                onClick={() => handleView(selectedLanguage)}
                className="w-full"
              >
                <Icon icon={FeatureIcons.eye} size="sm" className="mr-2" />
                {selectedLanguage === 'zh' ? '在线查看' : 'View Online'}
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                onClick={() => handleDownload(selectedLanguage)}
                className="w-full"
              >
                <Icon icon={ActionIcons.download} size="sm" className="mr-2" />
                {selectedLanguage === 'zh' ? '下载白皮书' : 'Download Whitepaper'}
              </Button>
            </div>

            {/* 提示信息 */}
            <div className="mt-4 p-3 bg-system-blue/10 rounded-lg">
              <p className="text-caption-1 text-system-blue">
                <Icon icon={FeatureIcons.info} size="sm" className="inline mr-1" />
                {selectedLanguage === 'zh' 
                  ? '白皮书包含项目的完整技术方案、代币经济学和发展路线图'
                  : 'The whitepaper contains complete technical solutions, tokenomics, and development roadmap'
                }
              </p>
            </div>
          </Card>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default WhitepaperModal;
