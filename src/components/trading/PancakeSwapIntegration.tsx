'use client';

import React from 'react';
import { ExternalLink, ArrowUpRight, TrendingUp } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON>, Badge } from '@/components/ui';
import { HAOX_TOKEN_ADDRESS, PANCAKESWAP_URLS } from '@/constants';

interface PancakeSwapIntegrationProps {
  className?: string;
}

export function PancakeSwapIntegration({ className }: PancakeSwapIntegrationProps) {
  const handleTradeOnPancakeSwap = () => {
    window.open(PANCAKESWAP_URLS.TRADE_HAOX, '_blank', 'noopener,noreferrer');
  };

  const handleAddLiquidity = () => {
    window.open(PANCAKESWAP_URLS.ADD_LIQUIDITY, '_blank', 'noopener,noreferrer');
  };

  const handleViewChart = () => {
    window.open(PANCAKESWAP_URLS.CHART, '_blank', 'noopener,noreferrer');
  };

  return (
    <Card className={className}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold flex items-center gap-2 text-label">
            <TrendingUp className="h-5 w-5 text-system-blue" />
            PancakeSwap 交易
          </h3>
          <p className="text-secondary-label mt-1">
            在 PancakeSwap 上交易 HAOX 代币
          </p>
        </div>
        <Badge className="bg-yellow-100 text-yellow-800">
          DEX
        </Badge>
      </div>
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <Button
            onClick={handleTradeOnPancakeSwap}
            className="flex items-center gap-2 h-12"
            variant="default"
          >
            <ExternalLink className="h-4 w-4" />
            交易 HAOX
            <ArrowUpRight className="h-3 w-3" />
          </Button>
          
          <Button
            onClick={handleAddLiquidity}
            className="flex items-center gap-2 h-12"
            variant="outline"
          >
            <ExternalLink className="h-4 w-4" />
            添加流动性
            <ArrowUpRight className="h-3 w-3" />
          </Button>
          
          <Button
            onClick={handleViewChart}
            className="flex items-center gap-2 h-12"
            variant="outline"
          >
            <ExternalLink className="h-4 w-4" />
            查看图表
            <ArrowUpRight className="h-3 w-3" />
          </Button>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">交易提示</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 确保您的钱包已连接到 BSC 网络</li>
            <li>• 准备足够的 BNB 作为交易手续费</li>
            <li>• 建议设置 1-3% 的滑点容忍度</li>
            <li>• 大额交易前请先小额测试</li>
          </ul>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-2">合约信息</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">代币合约:</span>
              <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                {HAOX_TOKEN_ADDRESS}
              </code>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">网络:</span>
              <span className="font-medium">BSC (BEP-20)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">符号:</span>
              <span className="font-medium">HAOX</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}

export default PancakeSwapIntegration;
