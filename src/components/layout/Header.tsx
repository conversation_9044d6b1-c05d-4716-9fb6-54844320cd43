'use client';

import React from 'react';
import Link from 'next/link';

import UserStatus from '@/components/auth/UserStatus';

const Header: React.FC = () => {
  const navigation = [
    { name: '首页', href: '/' },
    { name: '邀请', href: '/invitation' },
    { name: '排行榜', href: '/leaderboard' },
    { name: '任务', href: '/tasks' },
    { name: '个人中心', href: '/profile' },
  ];

  return (
    <header className="sticky top-0 z-40 bg-system-background/80 backdrop-blur-md border-b border-system-gray-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-system-blue to-system-purple rounded-lg" />
            <span className="text-xl font-bold text-label">SocioMint</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-secondary-label hover:text-label transition-colors duration-200"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            <UserStatus />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
