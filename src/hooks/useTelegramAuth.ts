/**
 * Telegram OAuth认证Hook
 * 处理Telegram登录和用户会话管理
 */

import { useState, useEffect, useCallback } from 'react';
import { TelegramUser } from '@/services/wallet/types';
import { custodyWalletService } from '@/services/wallet/CustodyWalletService';
import { useToast } from '@/components/ui';

interface UseTelegramAuthReturn {
  user: TelegramUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (telegramData: any) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
}



export const useTelegramAuth = (): UseTelegramAuthReturn => {
  const [user, setUser] = useState<TelegramUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { addToast } = useToast();



  /**
   * 初始化认证状态
   */
  useEffect(() => {
    // 强制客户端检查，避免SSR问题
    if (typeof window === "undefined") {
      return;
    }

    const initializeAuth = async () => {
      try {
        setIsLoading(true);

        // 开发环境从localStorage检查
        if (process.env.NODE_ENV === 'development') {
          const savedUser = localStorage.getItem('telegram-user');
          if (savedUser) {
            try {
              const user = JSON.parse(savedUser);
              setUser(user);
              setError(null);
            } catch (e) {
              localStorage.removeItem('telegram-user');
            }
          }
          // 开发环境也需要设置loading为false
          setIsLoading(false);
          return;
        }

        // 生产环境调用验证API检查认证状态
        const response = await fetch('/api/auth/verify');

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.user) {
            setUser(data.user);
            setError(null);
          }
        }
      } catch (err) {
        console.error('Failed to initialize auth:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    // 监听localStorage变化，实时更新认证状态
    const handleStorageChange = () => {
      if (process.env.NODE_ENV === 'development') {
        const savedUser = localStorage.getItem('telegram-user');
        if (savedUser) {
          try {
            const user = JSON.parse(savedUser);
            setUser(user);
            setError(null);
          } catch (e) {
            localStorage.removeItem('telegram-user');
            setUser(null);
          }
        } else {
          setUser(null);
        }
      }
    };

    // 添加事件监听器
    window.addEventListener('storage', handleStorageChange);

    // 清理函数
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  /**
   * Telegram登录 - 开发环境模拟实现
   */
  const login = useCallback(async (telegramData?: any) => {
    try {
      setIsLoading(true);
      setError(null);

      // 开发环境模拟登录
      if (process.env.NODE_ENV === 'development') {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 创建模拟用户
        const mockUser: TelegramUser = {
          id: 123456789,
          firstName: 'Demo',
          lastName: 'User',
          username: 'demo_user',
          photoUrl: undefined,
        };

        setUser(mockUser);

        // 保存到localStorage
        localStorage.setItem('telegram-user', JSON.stringify(mockUser));

        // 自动生成BSC钱包地址
        try {
          const walletAddress = await custodyWalletService.generateWalletForUser(mockUser.id);
          console.log(`为用户 ${mockUser.firstName} 生成钱包地址: ${walletAddress}`);

          addToast({
            type: 'success',
            title: '登录成功',
            message: `欢迎回来，${mockUser.firstName}！已为您生成BSC钱包地址。`,
          });
        } catch (walletError) {
          console.error('钱包生成失败:', walletError);
          addToast({
            type: 'success',
            title: '登录成功',
            message: `欢迎回来，${mockUser.firstName}！`,
          });
        }

        return;
      }

      // 生产环境真实登录
      const response = await fetch('/api/auth/telegram', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(telegramData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '登录失败');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || '登录失败');
      }

      // 设置用户信息
      const user: TelegramUser = {
        id: data.user.id,
        firstName: data.user.firstName,
        lastName: data.user.lastName,
        username: data.user.username,
        photoUrl: data.user.photoUrl,
      };

      setUser(user);

      // 自动生成BSC钱包地址
      try {
        const walletAddress = await custodyWalletService.generateWalletForUser(user.id);
        console.log(`为用户 ${user.firstName} 生成钱包地址: ${walletAddress}`);

        addToast({
          type: 'success',
          title: '登录成功',
          message: `欢迎回来，${user.firstName}！已为您生成BSC钱包地址。`,
        });
      } catch (walletError) {
        console.error('钱包生成失败:', walletError);
        addToast({
          type: 'success',
          title: '登录成功',
          message: `欢迎回来，${user.firstName}！`,
        });
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '登录失败';
      setError(errorMessage);

      addToast({
        type: 'error',
        title: '登录失败',
        message: errorMessage,
      });

      console.error('Telegram login failed:', err);
    } finally {
      setIsLoading(false);
    }
  }, [addToast]);

  /**
   * 登出
   */
  const logout = useCallback(async () => {
    try {
      // 开发环境清除localStorage
      if (process.env.NODE_ENV === 'development') {
        localStorage.removeItem('telegram-user');
      } else {
        // 生产环境调用登出API
        await fetch('/api/auth/logout', {
          method: 'POST',
        });
      }
    } catch (err) {
      console.error('Logout failed:', err);
    }

    setUser(null);
    setError(null);

    addToast({
      type: 'info',
      title: '已退出登录',
      message: '您已成功退出登录',
    });
  }, [addToast]);

  /**
   * 刷新用户信息
   */
  const refreshUser = useCallback(async () => {
    if (!user) return;

    try {
      // 调用验证API获取最新用户信息
      const response = await fetch('/api/auth/verify');

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.user) {
          setUser(data.user);
        } else {
          logout();
        }
      } else {
        logout();
      }
    } catch (err) {
      console.error('Failed to refresh user:', err);
      logout();
    }
  }, [user, logout]);



  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    logout,
    refreshUser,
  };
};

/**
 * Telegram Web App初始化Hook
 */
export const useTelegramWebApp = () => {
  const [webApp, setWebApp] = useState<any>(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // 检查是否在Telegram Web App环境中
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      
      // 初始化Telegram Web App
      tg.ready();
      tg.expand();
      
      // 设置主题
      tg.setHeaderColor('#007AFF');
      tg.setBackgroundColor('#FFFFFF');
      
      setWebApp(tg);
      setIsReady(true);
    }
  }, []);

  return {
    webApp,
    isReady,
    isInTelegram: !!webApp,
  };
};

/**
 * Telegram登录按钮Hook
 */
export const useTelegramLoginButton = (onAuth: (user: TelegramUser) => void) => {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // 动态加载Telegram登录脚本
    const script = document.createElement('script');
    script.src = 'https://telegram.org/js/telegram-widget.js?22';
    script.setAttribute('data-telegram-login', process.env.NEXT_PUBLIC_TELEGRAM_BOT_USERNAME || '');
    script.setAttribute('data-size', 'large');
    script.setAttribute('data-radius', '10');
    script.setAttribute('data-request-access', 'write');
    script.setAttribute('data-userpic', 'false');
    script.setAttribute('data-onauth', 'onTelegramAuth(user)');
    
    // 全局回调函数
    (window as any).onTelegramAuth = (user: any) => {
      setIsLoading(true);
      onAuth(user);
      setIsLoading(false);
    };

    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
      delete (window as any).onTelegramAuth;
    };
  }, [onAuth]);

  return { isLoading };
};

export default useTelegramAuth;
