'use client';

import React from 'react';
import { But<PERSON>, Icon, Card } from '@/components/ui';
import { NavigationIcons, FinanceIcons, UserIcons, RewardIcons } from '@/config/icons';
import Header from '@/components/layout/Header';
import PresaleSection from '@/components/presale/PresaleSection';

export default function Home() {
  const features = [
    {
      icon: FinanceIcons.trendingUp,
      title: 'PancakeSwap交易',
      description: '在PancakeSwap DEX上安全交易HAOX代币',
      color: 'text-system-blue',
    },
    {
      icon: UserIcons.users,
      title: '社交平台集成',
      description: '连接Twitter、Discord、Telegram等社交账户',
      color: 'text-system-purple',
    },
    {
      icon: RewardIcons.zap,
      title: '社交任务奖励',
      description: '完成社交任务获得HAOX代币奖励',
      color: 'text-system-orange',
    },
    {
      icon: UserIcons.shield,
      title: '安全可靠',
      description: '智能合约保障，多重安全机制保护用户资产',
      color: 'text-system-green',
    },
  ];

  return (
    <div className="min-h-screen bg-system-background">
      <Header />

      {/* 测试添加 PresaleSection */}
      <section className="py-8">
        <PresaleSection />
      </section>

      {/* 简化版本 - 只保留基本内容 */}
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">SocioMint - 社交挖矿平台</h1>
        
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-4">欢迎来到 SocioMint</h2>
            <p className="text-lg text-secondary-label">
              通过社交活动获得 HAOX 代币奖励
            </p>
          </div>
          
          <div className="flex justify-center">
            <Button className="flex items-center space-x-2">
              <span>开始交易</span>
              <Icon icon={NavigationIcons.forward} size="md" />
            </Button>
          </div>

          {/* 测试 Features 部分 */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16">
            {features.map((feature, index) => (
              <Card key={feature.title} className="text-center h-full">
                <div className={`w-16 h-16 rounded-2xl bg-system-gray-6 flex items-center justify-center mx-auto mb-4`}>
                  <Icon icon={feature.icon} size="xl" className={feature.color} />
                </div>
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-3">
                  {feature.title}
                </h3>
                <p className="text-body text-secondary-label">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
