'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Button, Card, Icon } from '@/components/ui';
import {
  NavigationIcons,
  FinanceIcons,
  UserIcons,
  RewardIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import PresaleSection from '@/components/presale/PresaleSection';
import PancakeSwapIntegration from '@/components/trading/PancakeSwapIntegration';
// import PerformanceMonitor from '@/components/performance/PerformanceMonitor';

export default function Home() {

  const features = [
    {
      icon: FinanceIcons.trendingUp,
      title: 'PancakeSwap交易',
      description: '在PancakeSwap DEX上安全交易HAOX代币',
      color: 'text-system-blue',
    },
    {
      icon: UserIcons.users,
      title: '社交平台集成',
      description: '连接Twitter、Discord、Telegram等社交账户',
      color: 'text-system-purple',
    },
    {
      icon: RewardIcons.zap,
      title: '社交任务奖励',
      description: '完成社交任务获得HAOX代币奖励',
      color: 'text-system-orange',
    },
    {
      icon: UserIcons.shield,
      title: '安全可靠',
      description: '智能合约保障，多重安全机制保护用户资产',
      color: 'text-system-green',
    },
  ];

  const stats = [
    { label: 'HAOX总供应量', value: '50亿枚' },
    { label: '注册用户', value: '10,000+' },
    { label: '社交任务完成', value: '50,000+' },
    { label: '代币奖励发放', value: '100万+' },
  ];

  return (
    <div className="min-h-screen bg-system-background">
      <Header />

      {/* 预售组件 - 移到顶部作为主要内容 */}
      <section className="py-8">
        <PresaleSection />
      </section>

      {/* PancakeSwap 交易集成 */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <PancakeSwapIntegration />
        </div>
      </section>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-system-blue/5 to-system-purple/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-large-title font-sf-pro font-bold text-label mb-6">
                社交化加密货币
                <br />
                <span className="text-system-blue">交易新体验</span>
              </h1>

              <p className="text-title-3 text-secondary-label mb-8 leading-relaxed">
                SocioMint 是首个集成社交平台的HAOX代币交易平台，
                通过社交任务获得奖励，享受安全便捷的数字资产交易体验。
              </p>

              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link href="/trade">
                  <Button size="lg" className="flex items-center space-x-2">
                    <span>开始交易</span>
                    <Icon icon={NavigationIcons.forward} size="md" />
                  </Button>
                </Link>

                <Link href="/merchants">
                  <Button variant="outline" size="lg">
                    成为商家
                  </Button>
                </Link>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-6">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    className="text-center"
                  >
                    <p className="text-title-2 font-sf-pro font-bold text-system-blue">
                      {stat.value}
                    </p>
                    <p className="text-caption-1 text-secondary-label">
                      {stat.label}
                    </p>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <div className="w-full h-96 bg-gradient-to-br from-system-blue to-system-purple rounded-3xl flex items-center justify-center">
                <div className="text-center text-white">
                  <div className="w-24 h-24 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Icon icon={FinanceIcons.trendUp} size="2xl" className="text-white" />
                  </div>
                  <h3 className="text-title-2 font-sf-pro font-bold mb-2">
                    HAOX代币
                  </h3>
                  <p className="text-body opacity-90">
                    下一代社交化数字资产
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>



      {/* Features Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-title-1 font-sf-pro font-bold text-label mb-4">
              为什么选择 SocioMint？
            </h2>
            <p className="text-title-3 text-secondary-label max-w-3xl mx-auto">
              我们将社交媒体的力量与区块链技术相结合，
              为用户提供前所未有的数字资产交易体验。
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                viewport={{ once: true }}
              >
                <Card className="text-center h-full">
                  <div className={`w-16 h-16 rounded-2xl bg-system-gray-6 flex items-center justify-center mx-auto mb-4`}>
                    <Icon icon={feature.icon} size="xl" className={feature.color} />
                  </div>
                  <h3 className="text-headline font-sf-pro font-semibold text-label mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-body text-secondary-label">
                    {feature.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* 邀请活动 Section */}
      <section className="py-20 bg-gradient-to-br from-system-blue/5 to-system-purple/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-title-1 font-sf-pro font-bold text-label mb-4">
              邀请好友，共享收益
            </h2>
            <p className="text-title-3 text-secondary-label max-w-3xl mx-auto">
              邀请朋友加入 SocioMint，每成功邀请1人可获得50 HAOX奖励，
              邀请越多奖励越丰厚，还有机会获得VIP商家资格！
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* 邀请奖励 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <div className="w-16 h-16 rounded-2xl bg-system-green/20 flex items-center justify-center mx-auto mb-4">
                  <Icon icon={UserIcons.users} size="xl" color="success" />
                </div>
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-3">
                  邀请奖励
                </h3>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">每邀请1人</span>
                    <span className="text-body font-sf-pro font-semibold text-system-green">+50 HAOX</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">邀请满5人</span>
                    <span className="text-body font-sf-pro font-semibold text-system-green">+300 HAOX</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">邀请满50人</span>
                    <span className="text-body font-sf-pro font-semibold text-system-green">VIP商家资格</span>
                  </div>
                </div>
                <Link href="/invitation">
                  <Button className="w-full bg-system-green hover:bg-green-600">
                    开始邀请
                  </Button>
                </Link>
              </Card>
            </motion.div>

            {/* 排行榜 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <div className="w-16 h-16 rounded-2xl bg-system-orange/20 flex items-center justify-center mx-auto mb-4">
                  <Icon icon={FinanceIcons.trendUp} size="xl" color="warning" />
                </div>
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-3">
                  邀请排行榜
                </h3>
                <p className="text-body text-secondary-label mb-4">
                  查看社区中最活跃的推广者，月度排行榜前三名可获得额外奖励：
                  第1名10000 HAOX，第2名5000 HAOX，第3名2000 HAOX
                </p>
                <Link href="/leaderboard">
                  <Button variant="outline" className="w-full border-system-orange text-system-orange hover:bg-system-orange/10">
                    查看排行榜
                  </Button>
                </Link>
              </Card>
            </motion.div>

            {/* 社交分享 */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <div className="w-16 h-16 rounded-2xl bg-system-blue/20 flex items-center justify-center mx-auto mb-4">
                  <Icon icon={UserIcons.shield} size="xl" color="primary" />
                </div>
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-3">
                  多平台分享
                </h3>
                <p className="text-body text-secondary-label mb-4">
                  支持一键分享到 Twitter、Telegram、WhatsApp 等社交平台，
                  轻松邀请好友加入，共同建设 SocioMint 社区
                </p>
                <Button
                  variant="outline"
                  className="w-full border-system-blue text-system-blue hover:bg-system-blue/10"
                  onClick={() => {
                    window.open('/invitation', '_blank');
                  }}
                >
                  获取邀请链接
                </Button>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-system-gray-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-title-1 font-sf-pro font-bold text-label mb-4">
              准备开始您的数字资产之旅？
            </h2>
            <p className="text-title-3 text-secondary-label mb-8">
              立即注册 SocioMint，体验社交化的加密货币交易
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/trade">
                <Button size="lg" className="flex items-center space-x-2">
                  <span>开始交易</span>
                  <Icon icon={NavigationIcons.forward} size="md" />
                </Button>
              </Link>
              <Link href="/invitation">
                <Button variant="outline" size="lg">
                  获取邀请链接
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-system-background border-t border-system-gray-4 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-system-blue to-system-purple rounded-lg" />
                <span className="text-title-3 font-sf-pro font-bold text-label">
                  SocioMint
                </span>
              </div>
              <p className="text-body text-secondary-label">
                社交化加密货币交易平台
              </p>
            </div>

            <div>
              <h4 className="text-headline font-sf-pro font-semibold text-label mb-4">
                产品
              </h4>
              <ul className="space-y-2">
                <li><Link href="/tasks" className="text-body text-secondary-label hover:text-label">社交任务</Link></li>
                <li><Link href="/presale" className="text-body text-secondary-label hover:text-label">代币预售</Link></li>
                <li><Link href="/leaderboard" className="text-body text-secondary-label hover:text-label">排行榜</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-headline font-sf-pro font-semibold text-label mb-4">
                支持
              </h4>
              <ul className="space-y-2">
                <li><Link href="/help" className="text-body text-secondary-label hover:text-label">帮助中心</Link></li>
                <li><Link href="/contact" className="text-body text-secondary-label hover:text-label">联系我们</Link></li>
                <li><Link href="/api" className="text-body text-secondary-label hover:text-label">API文档</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-headline font-sf-pro font-semibold text-label mb-4">
                法律
              </h4>
              <ul className="space-y-2">
                <li><Link href="/privacy" className="text-body text-secondary-label hover:text-label">隐私政策</Link></li>
                <li><Link href="/terms" className="text-body text-secondary-label hover:text-label">服务条款</Link></li>
                <li><Link href="/disclaimer" className="text-body text-secondary-label hover:text-label">免责声明</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-system-gray-4 mt-8 pt-8 text-center">
            <p className="text-body text-secondary-label">
              © 2024 SocioMint. 保留所有权利。
            </p>
          </div>
        </div>
      </footer>

      {/* 性能监控 - 暂时禁用 */}
      {/* <PerformanceMonitor
        enableConsoleLogging={process.env.NODE_ENV === 'development'}
        enableAnalytics={process.env.NODE_ENV === 'production'}
      /> */}
    </div>
  );
}