'use client';

import React from 'react';
import { <PERSON><PERSON>, Card, Icon } from '@/components/ui';
import { NavigationIcons } from '@/config/icons';

export default function TestMinimal() {
  return (
    <div className="min-h-screen bg-system-background">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">最小化测试页面</h1>
        
        <div className="space-y-4">
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">基础组件测试</h2>
            <div className="space-y-4">
              <Button>基础按钮</Button>
              <Button className="flex items-center space-x-2">
                <span>带图标的按钮</span>
                <Icon icon={NavigationIcons.forward} size="md" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
