'use client';

import React from 'react';
import { Icon } from '@/components/ui';
import { NavigationIcons, FinanceIcons, UserIcons, RewardIcons } from '@/config/icons';

export default function TestIcons() {
  return (
    <div className="min-h-screen bg-system-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">图标测试页面</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">导航图标</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Icon icon={NavigationIcons.home} size="md" />
                <span>Home</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon icon={NavigationIcons.forward} size="md" />
                <span>Forward</span>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4">金融图标</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Icon icon={FinanceIcons.trendUp} size="md" />
                <span>Trend Up</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon icon={FinanceIcons.trendingUp} size="md" />
                <span>Trending Up</span>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4">用户图标</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Icon icon={UserIcons.users} size="md" />
                <span>Users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon icon={UserIcons.shield} size="md" />
                <span>Shield</span>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4">奖励图标</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Icon icon={RewardIcons.zap} size="md" />
                <span>Zap</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon icon={RewardIcons.award} size="md" />
                <span>Award</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
