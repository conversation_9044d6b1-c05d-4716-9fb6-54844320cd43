'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading } from '@/components/ui';
import { 
  UserIcons, 
  FinanceIcons, 
  ActionIcons, 
  FeatureIcons,
  NavigationIcons 
} from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/layout/Header';
import UserInfo from '@/components/profile/UserInfo';
import WalletDashboard from '@/components/wallet/WalletDashboard';
import SafeTelegramLogin from '@/components/auth/SafeTelegramLogin';
// import OnboardingTour, { defaultOnboardingSteps, useOnboarding } from '@/components/onboarding/OnboardingTour';
// import HelpCenter from '@/components/help/HelpCenter';
import { cn } from '@/lib/utils';

const ProfilePage = () => {
  const { user: telegramUser, isAuthenticated: telegramAuth, refreshUser } = useTelegramAuth();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  // const { hasCompletedOnboarding, markOnboardingComplete } = useOnboarding();
  const [activeTab, setActiveTab] = useState<'wallet' | 'profile'>('wallet');
  const [isLoading, setIsLoading] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 确保只在客户端渲染
  useEffect(() => {
    setMounted(true);
  }, []);

  // 如果未认证，重定向到首页
  useEffect(() => {
    if (mounted && !isAuthenticated && !authLoading) {
      console.log('❌ 用户未认证，重定向到首页');
      window.location.href = '/';
    }
  }, [mounted, isAuthenticated, authLoading]);

  // 刷新数据
  const handleRefresh = async () => {
    setIsLoading(true);
    await refreshUser();
    setIsLoading(false);
  };

  const tabs = [
    {
      id: 'wallet' as const,
      name: '我的钱包',
      icon: FinanceIcons.wallet,
      description: '查看余额、转账和交易历史'
    },
    {
      id: 'profile' as const,
      name: '个人信息',
      icon: UserIcons.user,
      description: '管理个人信息和账户设置'
    }
  ];

  // 服务端渲染时显示加载状态
  if (!mounted || authLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <Card>
            <div className="text-center py-12">
              <Icon icon={UserIcons.user} size="3xl" color="primary" className="mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                请先登录
              </h3>
              <p className="text-body text-secondary-label mb-6">
                通过 Telegram 登录后即可访问您的个人中心
              </p>
              <SafeTelegramLogin
                size="lg"
                variant="primary"
                onSuccess={() => {
                  // 登录成功后刷新页面
                  window.location.reload();
                }}
                onError={(error) => {
                  console.error('个人中心登录失败:', error);
                }}
              />
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题和刷新按钮 */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-title-1 font-sf-pro font-bold text-label">
              个人中心
            </h1>
            <p className="text-body text-secondary-label mt-1">
              管理您的资产、交易和个人信息
            </p>
          </div>
          
          <Button
            variant="outline"
            size="md"
            onClick={handleRefresh}
            loading={isLoading}
            className="flex items-center space-x-2"
          >
            <Icon icon={ActionIcons.refresh} size="sm" />
            <span>刷新数据</span>
          </Button>
        </div>

        {/* 用户信息卡片 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <UserInfo user={user} />
        </motion.div>

        {/* 标签页导航 */}
        <div className="mb-8">
          <div className="border-b border-system-gray-4">
            <nav className="flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    'flex items-center space-x-2 py-4 px-1 border-b-2 font-sf-pro font-medium text-sm whitespace-nowrap transition-colors',
                    activeTab === tab.id
                      ? 'border-system-blue text-system-blue'
                      : 'border-transparent text-secondary-label hover:text-label hover:border-system-gray-3'
                  )}
                >
                  <Icon icon={tab.icon} size="sm" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 标签页内容 */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'wallet' && <WalletDashboard />}

          {activeTab === 'profile' && <UserInfo user={user} />}
        </motion.div>

        {/* 新手引导 - 暂时禁用 */}
        {/* {!hasCompletedOnboarding && (
          <OnboardingTour
            steps={defaultOnboardingSteps}
            onComplete={markOnboardingComplete}
            onSkip={markOnboardingComplete}
          />
        )} */}

        {/* 帮助中心 - 暂时禁用 */}
        {/* {showHelp && (
          <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
            <div className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <HelpCenter onClose={() => setShowHelp(false)} />
            </div>
          </div>
        )} */}

        {/* 帮助按钮 */}
        <button
          onClick={() => setShowHelp(true)}
          className="fixed bottom-6 right-6 w-14 h-14 bg-system-blue rounded-full shadow-lg flex items-center justify-center hover:bg-system-blue/90 transition-colors z-40"
        >
          <Icon icon={ActionIcons.helpCircle} size="lg" className="text-white" />
        </button>
      </div>
    </div>
  );
};

export default ProfilePage;
