'use client';

import React from 'react';
import { Icon } from '@/components/ui';
import { 
  NavigationIcons, 
  FinanceIcons, 
  UserIcons, 
  RewardIcons 
} from '@/config/icons';

export default function DebugIcons() {
  const testIcons = [
    { name: 'NavigationIcons.forward', icon: NavigationIcons.forward },
    { name: 'FinanceIcons.trendUp', icon: FinanceIcons.trendUp },
    { name: 'FinanceIcons.trendingUp', icon: FinanceIcons.trendingUp },
    { name: 'UserIcons.users', icon: UserIcons.users },
    { name: 'UserIcons.shield', icon: UserIcons.shield },
    { name: 'RewardIcons.zap', icon: RewardIcons.zap },
  ];

  return (
    <div className="min-h-screen bg-system-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">图标调试页面</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testIcons.map(({ name, icon }) => (
            <div key={name} className="p-4 border border-system-gray-4 rounded-lg">
              <div className="flex items-center space-x-4 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  {icon ? (
                    <Icon icon={icon} size="md" />
                  ) : (
                    <div className="w-6 h-6 bg-red-500 rounded" title="Icon is undefined" />
                  )}
                </div>
                <span className="text-sm font-mono">{name}</span>
              </div>
              <div className="text-xs text-secondary-label">
                Status: {icon ? '✅ Defined' : '❌ Undefined'}
              </div>
              {!icon && (
                <div className="text-xs text-red-600 mt-1">
                  Icon component is undefined!
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-8 p-4 bg-system-gray-6 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">原始图标对象检查</h2>
          <pre className="text-xs overflow-auto">
            {JSON.stringify({
              'NavigationIcons.forward': typeof NavigationIcons.forward,
              'FinanceIcons.trendUp': typeof FinanceIcons.trendUp,
              'FinanceIcons.trendingUp': typeof FinanceIcons.trendingUp,
              'UserIcons.users': typeof UserIcons.users,
              'UserIcons.shield': typeof UserIcons.shield,
              'RewardIcons.zap': typeof RewardIcons.zap,
            }, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
