import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'zh';
    
    const filename = lang === 'en' 
      ? 'HAOX_WHITEPAPER_V2_EN.md' 
      : 'HAOX_WHITEPAPER_V2.md';
    
    const filePath = join(process.cwd(), 'docs', filename);
    const content = await readFile(filePath, 'utf-8');
    
    return new NextResponse(content, {
      headers: {
        'Content-Type': 'text/markdown; charset=utf-8',
        'Content-Disposition': `inline; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('Error reading whitepaper:', error);
    return NextResponse.json(
      { error: 'Whitepaper not found' },
      { status: 404 }
    );
  }
}
