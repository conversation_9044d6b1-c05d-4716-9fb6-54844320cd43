'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon, Loading } from '@/components/ui';
import { 
  FeatureIcons, 
  ActionIcons, 
  NavigationIcons,
  FinanceIcons 
} from '@/config/icons';
import Header from '@/components/layout/Header';
import { cn } from '@/lib/utils';

interface WhitepaperSection {
  id: string;
  title: string;
  content: string[];
  subsections?: {
    title: string;
    content: string[];
  }[];
}

const WhitepaperPage = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('introduction');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // 模拟加载
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const sections: WhitepaperSection[] = [
    {
      id: 'introduction',
      title: '项目介绍',
      content: [
        'SocioMint 是一个创新的社交化加密货币生态系统，旨在通过社交媒体活动为用户提供数字资产奖励。',
        '我们的平台集成了多个社交媒体平台，让用户通过完成社交任务获得 HAOX 代币奖励。',
        '项目致力于构建一个去中心化的社交挖矿平台，让每个用户都能从社交活动中获得价值。'
      ]
    },
    {
      id: 'tokenomics',
      title: '代币经济学',
      content: [
        'HAOX 代币是 SocioMint 生态系统的核心，总供应量为 50 亿枚。',
        '代币分配遵循公平、透明的原则，确保生态系统的长期可持续发展。'
      ],
      subsections: [
        {
          title: '代币分配',
          content: [
            '• 社交挖矿奖励：40% (20亿枚)',
            '• 预售：20% (10亿枚)',
            '• 团队与顾问：15% (7.5亿枚)',
            '• 生态系统发展：15% (7.5亿枚)',
            '• 流动性池：10% (5亿枚)'
          ]
        },
        {
          title: '释放机制',
          content: [
            '• 社交挖矿奖励：线性释放，为期5年',
            '• 预售代币：TGE后立即释放',
            '• 团队代币：12个月锁定期，之后24个月线性释放',
            '• 生态系统基金：根据发展需要逐步释放'
          ]
        }
      ]
    },
    {
      id: 'technology',
      title: '技术架构',
      content: [
        'SocioMint 基于 Binance Smart Chain (BSC) 构建，确保低成本、高效率的交易体验。',
        '我们采用智能合约技术确保所有交易的透明性和安全性。'
      ],
      subsections: [
        {
          title: '核心技术',
          content: [
            '• 智能合约：基于 Solidity 开发的安全合约',
            '• 社交API集成：支持多平台社交媒体接入',
            '• 去中心化存储：使用 IPFS 存储用户数据',
            '• 跨链桥接：支持多链资产转移'
          ]
        }
      ]
    },
    {
      id: 'roadmap',
      title: '发展路线图',
      content: [
        '我们制定了详细的发展计划，分阶段实现项目目标。'
      ],
      subsections: [
        {
          title: '2024 Q4',
          content: [
            '• 平台测试版发布',
            '• 社交媒体集成',
            '• 预售活动启动',
            '• 社区建设'
          ]
        },
        {
          title: '2025 Q1',
          content: [
            '• 主网正式上线',
            '• DEX 交易支持',
            '• 移动端应用发布',
            '• 合作伙伴拓展'
          ]
        },
        {
          title: '2025 Q2-Q4',
          content: [
            '• NFT 市场集成',
            '• DAO 治理机制',
            '• 跨链功能实现',
            '• 全球市场扩展'
          ]
        }
      ]
    }
  ];

  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loading text="加载白皮书中..." />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-label mb-4">
            SocioMint 白皮书
          </h1>
          <p className="text-lg text-secondary-label max-w-3xl mx-auto">
            深入了解 SocioMint 的技术架构、代币经济学和发展愿景
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* 侧边导航 */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <h3 className="text-lg font-semibold text-label mb-4">目录</h3>
              <nav className="space-y-2">
                {sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={cn(
                      'w-full text-left px-3 py-2 rounded-lg transition-colors',
                      'hover:bg-system-gray-5',
                      activeSection === section.id
                        ? 'bg-system-blue text-white'
                        : 'text-secondary-label'
                    )}
                  >
                    {section.title}
                  </button>
                ))}
              </nav>
              
              <div className="mt-6 pt-6 border-t border-system-gray-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => window.open('/api/whitepaper', '_blank')}
                >
                  <Icon icon={ActionIcons.download} size="sm" className="mr-2" />
                  下载 PDF
                </Button>
              </div>
            </Card>
          </div>

          {/* 主要内容 */}
          <div className="lg:col-span-3">
            {sections.map((section) => (
              <motion.div
                key={section.id}
                initial={{ opacity: 0 }}
                animate={{ 
                  opacity: activeSection === section.id ? 1 : 0,
                  display: activeSection === section.id ? 'block' : 'none'
                }}
                transition={{ duration: 0.3 }}
              >
                <Card className="mb-8">
                  <h2 className="text-2xl font-bold text-label mb-6">
                    {section.title}
                  </h2>
                  
                  <div className="space-y-4">
                    {section.content.map((paragraph, index) => (
                      <p key={index} className="text-body text-secondary-label leading-relaxed">
                        {paragraph}
                      </p>
                    ))}
                  </div>

                  {section.subsections && (
                    <div className="mt-8 space-y-6">
                      {section.subsections.map((subsection, index) => (
                        <div key={index} className="border-l-4 border-system-blue pl-6">
                          <h3 className="text-lg font-semibold text-label mb-3">
                            {subsection.title}
                          </h3>
                          <div className="space-y-2">
                            {subsection.content.map((item, itemIndex) => (
                              <p key={itemIndex} className="text-body text-secondary-label">
                                {item}
                              </p>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhitepaperPage;
