// HAOX Token Constants
export const HAOX_TOKEN = {
  NAME: 'HAOX',
  SYMBOL: 'HAOX',
  TOTAL_SUPPLY: '5000000000', // 50亿枚
  DECIMALS: 18,
  CONTRACT_ADDRESS: process.env.NEXT_PUBLIC_HAOX_TOKEN_ADDRESS || '******************************************',
} as const;

// Token address constant for external use
export const HAOX_TOKEN_ADDRESS = HAOX_TOKEN.CONTRACT_ADDRESS;

// Supported Networks
export const SUPPORTED_NETWORKS = {
  ETHEREUM: {
    id: 1,
    name: 'Ethereum',
    rpcUrl: 'https://mainnet.infura.io/v3/',
    blockExplorer: 'https://etherscan.io',
  },
  BSC: {
    id: 56,
    name: 'Binance Smart Chain',
    rpcUrl: 'https://bsc-dataseed.binance.org/',
    blockExplorer: 'https://bscscan.com',
  },
  POLYGON: {
    id: 137,
    name: 'Polygon',
    rpcUrl: 'https://polygon-rpc.com/',
    blockExplorer: 'https://polygonscan.com',
  },
} as const;

// Social Platforms
export const SOCIAL_PLATFORMS = {
  TWITTER: {
    name: 'Twitter',
    icon: 'Twitter',
    color: '#1DA1F2',
    baseUrl: 'https://twitter.com',
  },
  DISCORD: {
    name: 'Discord',
    icon: 'MessageCircle',
    color: '#5865F2',
    baseUrl: 'https://discord.com',
  },
  TELEGRAM: {
    name: 'Telegram',
    icon: 'Send',
    color: '#0088CC',
    baseUrl: 'https://t.me',
  },
} as const;

// Payment Methods
export const PAYMENT_METHODS = {
  ALIPAY: {
    name: '支付宝',
    icon: 'Smartphone',
    color: '#1677FF',
    currency: 'CNY',
  },
  WECHAT: {
    name: '微信支付',
    icon: 'MessageCircle',
    color: '#07C160',
    currency: 'CNY',
  },
  CRYPTO: {
    name: '加密货币',
    icon: 'Wallet',
    color: '#F7931A',
    currency: 'ETH',
  },
} as const;

// Transaction Status
export const TRANSACTION_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

// Merchant Status
export const MERCHANT_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
} as const;

// Task Types
export const TASK_TYPES = {
  FOLLOW: 'follow',
  LIKE: 'like',
  RETWEET: 'retweet',
  JOIN: 'join',
  SHARE: 'share',
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    PROFILE: '/api/auth/profile',
  },
  TOKENS: {
    HAOX: '/api/tokens/haox',
    PRICE: '/api/tokens/price',
  },
  SOCIAL: {
    CONNECT: '/api/social/connect',
    DISCONNECT: '/api/social/disconnect',
    TASKS: '/api/social/tasks',
    COMPLETE_TASK: '/api/social/complete-task',
  },
} as const;

// UI Constants
export const BREAKPOINTS = {
  SM: '640px',
  MD: '768px',
  LG: '1024px',
  XL: '1280px',
  '2XL': '1536px',
} as const;

// Animation Durations
export const ANIMATION_DURATION = {
  FAST: 0.15,
  NORMAL: 0.3,
  SLOW: 0.5,
} as const;

// Apple Design System Colors
export const COLORS = {
  SYSTEM_BLUE: '#007AFF',
  SYSTEM_GREEN: '#34C759',
  SYSTEM_INDIGO: '#5856D6',
  SYSTEM_ORANGE: '#FF9500',
  SYSTEM_PINK: '#FF2D92',
  SYSTEM_PURPLE: '#AF52DE',
  SYSTEM_RED: '#FF3B30',
  SYSTEM_TEAL: '#5AC8FA',
  SYSTEM_YELLOW: '#FFCC00',
  SYSTEM_GRAY: '#8E8E93',
  SYSTEM_GRAY2: '#AEAEB2',
  SYSTEM_GRAY3: '#C7C7CC',
  SYSTEM_GRAY4: '#D1D1D6',
  SYSTEM_GRAY5: '#E5E5EA',
  SYSTEM_GRAY6: '#F2F2F7',
  LABEL: '#000000',
  SECONDARY_LABEL: '#3C3C43',
  TERTIARY_LABEL: '#3C3C43',
  QUATERNARY_LABEL: '#3C3C43',
  SYSTEM_BACKGROUND: '#FFFFFF',
  SECONDARY_SYSTEM_BACKGROUND: '#F2F2F7',
  TERTIARY_SYSTEM_BACKGROUND: '#FFFFFF',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接错误，请检查您的网络设置',
  WALLET_NOT_CONNECTED: '请先连接您的钱包',
  INSUFFICIENT_BALANCE: '余额不足',
  TRANSACTION_FAILED: '交易失败，请重试',
  INVALID_ADDRESS: '无效的钱包地址',
  UNAUTHORIZED: '未授权访问',
  SERVER_ERROR: '服务器错误，请稍后重试',
  VALIDATION_ERROR: '输入信息有误，请检查后重试',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  TRANSACTION_SUCCESS: '交易成功完成',
  WALLET_CONNECTED: '钱包连接成功',
  PROFILE_UPDATED: '个人资料更新成功',
  TASK_COMPLETED: '任务完成，奖励已发放',
  MERCHANT_APPLICATION_SUBMITTED: '商家申请已提交，请等待审核',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  WALLET_CONNECTOR: 'wallet-connector',
  USER_PREFERENCES: 'user-preferences',
  USER_PROFILE: 'user-profile',
  USER_SETTINGS: 'user-settings',
  THEME: 'theme',
  LANGUAGE: 'language',
  AUTH_TOKEN: 'auth-token',
  LAST_LOGIN: 'last-login',
} as const;

// Default Values
export const DEFAULTS = {
  PAGE_SIZE: 20,
  DEBOUNCE_DELAY: 300,
  TOAST_DURATION: 3000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// External URLs
export const EXTERNAL_URLS = {
  TELEGRAM: 'https://t.me/HAOXOfficial',
  TWITTER: 'https://twitter.com/HAOXToken',
  DISCORD: 'https://discord.gg/haox',
  GITHUB: 'https://github.com/haox-project',
  DOCS: 'https://docs.haox.io',
  WHITEPAPER: 'https://haox.io/whitepaper',
} as const;

// PancakeSwap URLs
export const PANCAKESWAP_URLS = {
  TRADE_HAOX: `https://pancakeswap.finance/swap?outputCurrency=${HAOX_TOKEN_ADDRESS}`,
  ADD_LIQUIDITY: `https://pancakeswap.finance/add/${HAOX_TOKEN_ADDRESS}`,
  CHART: `https://pancakeswap.finance/info/tokens/${HAOX_TOKEN_ADDRESS}`,
  POOL: `https://pancakeswap.finance/info/pools`,
} as const;
