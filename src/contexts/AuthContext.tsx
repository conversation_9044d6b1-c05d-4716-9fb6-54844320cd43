'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { UserProfile } from '@/types/user';
import { userProfileService } from '@/services/user/UserProfileService';

interface TelegramUser {
  id: number;
  firstName: string;
  lastName?: string;
  username?: string;
  photoUrl?: string;
}

interface AuthContextType {
  user: UserProfile | null;
  telegramUser: TelegramUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (userData: TelegramUser) => void;
  logout: () => void;
  refreshAuth: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [telegramUser, setTelegramUser] = useState<TelegramUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  // 确保只在客户端运行
  useEffect(() => {
    setMounted(true);
  }, []);

  // 从localStorage加载用户数据
  const loadUserFromStorage = async () => {
    if (typeof window === 'undefined') return;

    try {
      const savedUser = localStorage.getItem('telegram-user');
      if (savedUser) {
        const telegramUserData = JSON.parse(savedUser);
        setTelegramUser(telegramUserData);

        // 加载用户资料
        const userProfile = await userProfileService.getUserProfile(telegramUserData.id);
        if (userProfile) {
          setUser(userProfile);
          console.log('✅ 从localStorage加载用户数据:', { telegramUserData, userProfile });
        } else {
          console.warn('⚠️ 用户资料未找到，创建默认资料');
          setUser(null);
        }
      } else {
        setTelegramUser(null);
        setUser(null);
      }
    } catch (error) {
      console.error('❌ 加载用户数据失败:', error);
      localStorage.removeItem('telegram-user');
      setTelegramUser(null);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化认证状态
  useEffect(() => {
    if (!mounted) return;
    
    loadUserFromStorage();
  }, [mounted]);

  // 监听localStorage变化
  useEffect(() => {
    if (!mounted) return;

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'telegram-user') {
        console.log('🔄 检测到localStorage变化，重新加载用户数据');
        loadUserFromStorage();
      }
    };

    const handleCustomStorageEvent = () => {
      console.log('🔄 检测到自定义存储事件，重新加载用户数据');
      loadUserFromStorage();
    };

    // 监听标准storage事件和自定义事件
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('storage', handleCustomStorageEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('storage', handleCustomStorageEvent);
    };
  }, [mounted]);

  const login = async (userData: TelegramUser) => {
    try {
      console.log('🚀 执行登录，用户数据:', userData);
      localStorage.setItem('telegram-user', JSON.stringify(userData));
      setTelegramUser(userData);

      // 加载或创建用户资料
      const userProfile = await userProfileService.getUserProfile(userData.id);
      if (userProfile) {
        setUser(userProfile);
      }

      // 触发存储事件通知其他组件
      window.dispatchEvent(new Event('storage'));

      console.log('✅ 登录成功，认证状态已更新');
    } catch (error) {
      console.error('❌ 登录失败:', error);
    }
  };

  const logout = () => {
    try {
      console.log('🚪 执行登出');
      localStorage.removeItem('telegram-user');
      setTelegramUser(null);
      setUser(null);

      // 触发存储事件通知其他组件
      window.dispatchEvent(new Event('storage'));

      console.log('✅ 登出成功');
    } catch (error) {
      console.error('❌ 登出失败:', error);
    }
  };

  const refreshAuth = () => {
    console.log('🔄 刷新认证状态');
    loadUserFromStorage();
  };

  const value: AuthContextType = {
    user,
    telegramUser,
    isAuthenticated: !!user,
    isLoading: !mounted || isLoading,
    login,
    logout,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
